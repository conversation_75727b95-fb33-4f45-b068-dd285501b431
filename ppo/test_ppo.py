#!/usr/bin/env python3
"""
PPO训练测试脚本
测试改进后的PPO实现
"""

import torch
import numpy as np
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ppo.memory_env import MemoryEnv, X7F9A2B
from ppo.ppo_policy import PPOPolicy, K3M8N1P
from ppo.ppo_train import Z9Q4R7S, X9K2L7M, PPOTrainer, ExperienceBuffer


def create_dummy_gru():
    """创建一个简单的虚拟GRU模型用于测试"""
    class DummyGRU(torch.nn.Module):
        def __init__(self):
            super().__init__()
            self.linear = torch.nn.Linear(3, 1)

        def forward(self, x, hidden=None):
            # x shape: (batch, seq_len, 3)
            output = self.linear(x[:, -1, :])  # 只使用最后一个时间步
            return output, None

    return DummyGRU()


def test_experience_buffer():
    """测试经验缓冲区"""
    print("=== 测试经验缓冲区 ===")

    buffer = X9K2L7M(capacity=5)

    # 添加一些虚拟经验
    for i in range(3):
        obs = {"r_history": np.random.rand(10, 1),
               "t_history": np.random.rand(10, 1),
               "p_history": np.random.rand(10, 1),
               "days_since_last": np.array([i])}
        action = torch.tensor(i % 2)
        reward = float(i)
        value = torch.tensor([i * 0.5])
        log_prob = torch.tensor([-0.5])
        done = i == 2

        buffer.store(obs, action, reward, value, log_prob, done)

    print(f"缓冲区大小: {len(buffer)}")

    # 获取批量数据
    batch = buffer.get_batch()
    print(f"批量数据键: {batch.keys()}")
    print(f"动作形状: {batch['actions'].shape}")
    print(f"奖励形状: {batch['rewards'].shape}")

    buffer.clear()
    print(f"清空后缓冲区大小: {len(buffer)}")
    print("✓ 经验缓冲区测试通过\n")


def test_environment():
    """测试环境"""
    print("=== 测试环境 ===")

    dummy_gru = create_dummy_gru()
    env = X7F9A2B(dummy_gru, max_history=5)

    print(f"动作空间: {env.action_space}")
    print(f"观测空间: {env.observation_space}")

    # 重置环境
    obs = env.reset()
    print(f"初始观测键: {obs.keys()}")
    print(f"r_history形状: {obs['r_history'].shape}")

    # 执行几步
    for i in range(3):
        action = i % 2
        next_obs, reward, done, info = env.step(action)
        print(f"步骤 {i}: 动作={action}, 奖励={reward:.3f}, 完成={done}")
        obs = next_obs

    print("✓ 环境测试通过\n")


def test_policy():
    """测试策略网络"""
    print("=== 测试策略网络 ===")

    dummy_gru = create_dummy_gru()
    policy = K3M8N1P(dummy_gru)

    # 创建虚拟观测
    obs = {
        "r_history": np.random.rand(10, 1).astype(np.float32),
        "t_history": np.random.rand(10, 1).astype(np.float32),
        "p_history": np.random.rand(10, 1).astype(np.float32),
        "days_since_last": np.array([5.0], dtype=np.float32)
    }

    # 前向传播
    dist, value = policy(obs)
    action = dist.sample()
    log_prob = dist.log_prob(action)

    print(f"动作: {action}")
    print(f"值: {value}")
    print(f"对数概率: {log_prob}")
    print(f"熵: {dist.entropy()}")

    print("✓ 策略网络测试通过\n")


def test_trainer():
    """测试训练器"""
    print("=== 测试训练器 ===")

    dummy_gru = create_dummy_gru()
    env = X7F9A2B(dummy_gru, max_history=5)
    policy = K3M8N1P(dummy_gru)

    # 使用混淆类名创建训练器
    trainer = Z9Q4R7S(env, policy, x3=1e-3, x4=0.99, x5=0.2, x6=2, x7=10)

    print("开始短期训练测试...")
    stats = trainer.train(total_steps=50)

    print(f"训练统计:")
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.4f}")
        else:
            print(f"  {key}: {value}")

    print("✓ 训练器测试通过\n")


def test_compatibility():
    """测试兼容性别名"""
    print("=== 测试兼容性别名 ===")

    # 测试别名是否正确指向原类
    assert PPOTrainer == Z9Q4R7S, "PPOTrainer别名错误"
    assert ExperienceBuffer == X9K2L7M, "ExperienceBuffer别名错误"
    assert K3M8N1P == PPOPolicy, "K3M8N1P别名错误"
    assert X7F9A2B == MemoryEnv, "X7F9A2B别名错误"

    print("✓ 所有别名测试通过\n")


if __name__ == "__main__":
    print("开始PPO实现测试...\n")

    try:
        test_experience_buffer()
        test_environment()
        test_policy()
        test_trainer()
        test_compatibility()

        print("🎉 所有测试通过！PPO实现工作正常。")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
