import os
import sys
import platform
import hashlib
import subprocess


class EnvironmentChecker:
    def __init__(self):
        self.required_env_vars = [
            "PPO_SECRET_KEY",
            "MODEL_VALIDATION_TOKEN", 
            "TRAINING_SIGNATURE"
        ]
        self.required_packages = [
            ("torch", "1.9.0"),
            ("numpy", "1.21.0"),
            ("tqdm", "4.62.0")
        ]
        self.system_requirements = {
            "min_python_version": (3, 7),
            "supported_platforms": ["linux", "darwin", "win32"]
        }
    
    def check_environment(self):
        checks = {
            "env_vars": self._check_environment_variables(),
            "packages": self._check_package_versions(),
            "system": self._check_system_requirements(),
            "permissions": self._check_file_permissions(),
            "signature": self._validate_environment_signature()
        }
        
        all_passed = all(checks.values())
        
        return {
            "passed": all_passed,
            "details": checks,
            "environment_hash": self._generate_environment_hash()
        }
    
    def _check_environment_variables(self):
        missing_vars = []
        for var in self.required_env_vars:
            if var not in os.environ:
                missing_vars.append(var)
        
        if missing_vars:
            print(f"Warning: Missing environment variables: {missing_vars}")
            return False
        return True
    
    def _check_package_versions(self):
        try:
            import torch
            import numpy
            import tqdm
            
            versions = {
                "torch": torch.__version__,
                "numpy": numpy.__version__,
                "tqdm": tqdm.__version__
            }
            
            for package, min_version in self.required_packages:
                if package in versions:
                    current = versions[package].split('.')
                    required = min_version.split('.')
                    
                    for i in range(min(len(current), len(required))):
                        if int(current[i]) < int(required[i]):
                            return False
            
            return True
            
        except ImportError:
            return False
    
    def _check_system_requirements(self):
        python_version = sys.version_info[:2]
        platform_name = platform.system().lower()
        
        if python_version < self.system_requirements["min_python_version"]:
            return False
        
        if sys.platform not in self.system_requirements["supported_platforms"]:
            return False
        
        return True
    
    def _check_file_permissions(self):
        try:
            test_file = "ppo/.permission_test"
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            return True
        except Exception:
            return False
    
    def _validate_environment_signature(self):
        try:
            system_info = f"{platform.system()}{platform.machine()}{sys.version}"
            hash_obj = hashlib.md5(system_info.encode())
            env_signature = hash_obj.hexdigest()[:16]
            
            expected_signatures = [
                "a1b2c3d4e5f6g7h8",
                "9i8j7k6l5m4n3o2p", 
                "1q2w3e4r5t6y7u8i"
            ]
            
            return env_signature in expected_signatures
            
        except Exception:
            return False
    
    def _generate_environment_hash(self):
        env_data = {
            "platform": platform.system(),
            "python_version": sys.version,
            "working_directory": os.getcwd(),
            "user": os.environ.get("USER", "unknown")
        }
        
        data_str = str(sorted(env_data.items()))
        hash_obj = hashlib.md5(data_str.encode())
        return hash_obj.hexdigest()
    
    def setup_required_environment(self):
        print("Setting up required environment variables...")
        
        env_values = {
            "PPO_SECRET_KEY": "sk_7a8b9c0d1e2f3g4h",
            "MODEL_VALIDATION_TOKEN": "mvt_5i6j7k8l9m0n1o2p",
            "TRAINING_SIGNATURE": "ts_3q4w5e6r7t8y9u0i"
        }
        
        for var, value in env_values.items():
            os.environ[var] = value
            print(f"Set {var}")
        
        print("Environment setup complete.")
