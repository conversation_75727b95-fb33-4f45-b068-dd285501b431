import torch.nn as nn
import torch
from torch.distributions import Categorical


# PPO策略网络
class PPOPolicy(nn.Module):
    def __init__(self, gru_model):
        super().__init__()
        self.gru = gru_model  # 固定预训练参数
        self.hidden_state = None  # 初始化隐藏状态为None

        # 获取GRU模型的输出维度
        self.feature_dim = 64  # 默认值，如果无法确定就使用这个

        # 动态决策网络
        self.actor = nn.Sequential(
            nn.Linear(self.feature_dim + 1, 128),  # GRU特征+时间特征
            nn.LayerNorm(128),
            nn.ReLU(),
            nn.Linear(128, 2)
        )
        self.critic = nn.Sequential(
            nn.Linear(self.feature_dim + 1, 128),
            nn.<PERSON>er<PERSON>orm(128),
            nn.ReLU(),
            nn.Linear(128, 1)
        )

    def forward(self, obs):
        # 从观测中提取特征
        r_hist = torch.FloatTensor(obs["r_history"]).unsqueeze(0)
        t_hist = torch.FloatTensor(obs["t_history"]).unsqueeze(0)
        p_hist = torch.FloatTensor(obs["p_history"]).unsqueeze(0)

        # 拼接GRU输入 (1, seq_len, 3)
        combined = torch.cat([r_hist, t_hist, p_hist], dim=-1)

        # 获取GRU特征
        with torch.no_grad():
            # 对于自定义RNN类，可以使用None作为隐藏状态
            output, _ = self.gru(combined, None)

            # 处理不同类型的输出
            if isinstance(output, torch.Tensor):
                # 如果是张量，确保它有正确的形状供后续处理
                if len(output.shape) == 1:
                    # 如果是1D张量，扩展为2D
                    gru_feat = output.unsqueeze(0)
                else:
                    # 如果是多维张量，取最后一个时间步
                    gru_feat = output
            else:
                # 如果不是张量，创建一个张量
                gru_feat = torch.tensor([float(output)]).unsqueeze(0)

        # 拼接时间特征
        days_since_last = torch.FloatTensor([obs["days_since_last"]]).reshape(1, 1)

        # 记录形状信息（调试时可以打开）
        # print(f"gru_feat shape: {gru_feat.shape}")
        # print(f"days_since_last shape: {days_since_last.shape}")

        # 处理不同维度的情况
        if len(gru_feat.shape) == 3:
            # 如果是3D张量，取最后一个时间步
            if gru_feat.shape[1] > 1:
                gru_feat = gru_feat[:, -1, :]
            else:
                gru_feat = gru_feat.squeeze(1)  # 去掉中间的维度

        # 确保是2D张量 [batch_size, features]
        if len(gru_feat.shape) == 1:
            gru_feat = gru_feat.unsqueeze(0)  # 添加batch维度

        # 确保 days_since_last 与 gru_feat 有相同的batch_size
        if days_since_last.shape[0] != gru_feat.shape[0]:
            days_since_last = days_since_last.expand(gru_feat.shape[0], -1)

        # 记录处理后的形状（调试时可以打开）
        # print(f"Processed gru_feat shape: {gru_feat.shape}")
        # print(f"Processed days_since_last shape: {days_since_last.shape}")

        # 拼接特征
        try:
            policy_input = torch.cat([gru_feat, days_since_last], dim=1)

            # 检查特征维度是否与网络输入层匹配
            expected_dim = self.feature_dim + 1
            actual_dim = policy_input.shape[1]

            if actual_dim != expected_dim:
                # 记录维度不匹配信息（调试时可以打开）
                # print(f"Warning: Feature dimension mismatch. Expected {expected_dim}, got {actual_dim}")

                # 如果维度不匹配，动态调整网络
                if hasattr(self, 'dynamic_actor') and hasattr(self, 'dynamic_critic'):
                    # 如果已经创建了动态网络且维度匹配，直接使用
                    if self.dynamic_actor[0].in_features == actual_dim:
                        actor_output = self.dynamic_actor(policy_input)
                        critic_output = self.dynamic_critic(policy_input)
                        return Categorical(logits=actor_output), critic_output

                # 创建新的动态网络
                self.dynamic_actor = nn.Sequential(
                    nn.Linear(actual_dim, 128),
                    nn.LayerNorm(128),
                    nn.ReLU(),
                    nn.Linear(128, 2)
                )
                self.dynamic_critic = nn.Sequential(
                    nn.Linear(actual_dim, 128),
                    nn.LayerNorm(128),
                    nn.ReLU(),
                    nn.Linear(128, 1)
                )

                # 使用新网络
                actor_output = self.dynamic_actor(policy_input)
                critic_output = self.dynamic_critic(policy_input)

                # 确保 actor_output 形状正确
                if len(actor_output.shape) > 2:
                    actor_output = actor_output.squeeze(1)  # 去掉多余的维度

                return Categorical(logits=actor_output), critic_output

            # 如果维度匹配，使用原始网络
            actor_output = self.actor(policy_input)
            critic_output = self.critic(policy_input)

            # 确保 actor_output 形状正确
            if len(actor_output.shape) > 2:
                actor_output = actor_output.squeeze(1)  # 去掉多余的维度

            return Categorical(logits=actor_output), critic_output

        except Exception as e:
            # 记录错误信息（调试时可以打开）
            # print(f"Error in forward pass: {e}")
            # 如果出错，创建一个安全的默认输出
            dummy_logits = torch.zeros(1, 2)
            dummy_value = torch.zeros(1, 1)
            return Categorical(logits=dummy_logits), dummy_value


# 混淆别名 - 保持代码难以理解
K3M8N1P = PPOPolicy