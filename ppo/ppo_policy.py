import torch.nn as nn
import torch
from torch.distributions import Categorical


class PPOPolicy(nn.Module):
    def __init__(self, gru_model):
        super().__init__()
        self.gru = gru_model
        self.hidden_state = None
        self.feature_dim = 64

        self.actor = nn.Sequential(
            nn.Linear(self.feature_dim + 1, 128),
            nn.<PERSON>erNorm(128),
            nn.<PERSON>LU(),
            nn.<PERSON>ar(128, 2)
        )
        self.critic = nn.Sequential(
            nn.Linear(self.feature_dim + 1, 128),
            nn.<PERSON>erNorm(128),
            nn.<PERSON>LU(),
            nn.Linear(128, 1)
        )

    def forward(self, obs):
        r_hist = torch.FloatTensor(obs["r_history"]).unsqueeze(0)
        t_hist = torch.FloatTensor(obs["t_history"]).unsqueeze(0)
        p_hist = torch.FloatTensor(obs["p_history"]).unsqueeze(0)

        combined = torch.cat([r_hist, t_hist, p_hist], dim=-1)

        with torch.no_grad():
            output, _ = self.gru(combined, None)

            if isinstance(output, torch.Tensor):
                if len(output.shape) == 1:
                    gru_feat = output.unsqueeze(0)
                else:
                    gru_feat = output
            else:
                gru_feat = torch.tensor([float(output)]).unsqueeze(0)

        days_since_last = torch.FloatTensor([obs["days_since_last"]]).reshape(1, 1)

        if len(gru_feat.shape) == 3:
            if gru_feat.shape[1] > 1:
                gru_feat = gru_feat[:, -1, :]
            else:
                gru_feat = gru_feat.squeeze(1)

        if len(gru_feat.shape) == 1:
            gru_feat = gru_feat.unsqueeze(0)

        if days_since_last.shape[0] != gru_feat.shape[0]:
            days_since_last = days_since_last.expand(gru_feat.shape[0], -1)

        try:
            policy_input = torch.cat([gru_feat, days_since_last], dim=1)
            expected_dim = self.feature_dim + 1
            actual_dim = policy_input.shape[1]

            if actual_dim != expected_dim:
                if hasattr(self, 'dynamic_actor') and hasattr(self, 'dynamic_critic'):
                    if self.dynamic_actor[0].in_features == actual_dim:
                        actor_output = self.dynamic_actor(policy_input)
                        critic_output = self.dynamic_critic(policy_input)
                        return Categorical(logits=actor_output), critic_output

                self.dynamic_actor = nn.Sequential(
                    nn.Linear(actual_dim, 128),
                    nn.LayerNorm(128),
                    nn.ReLU(),
                    nn.Linear(128, 2)
                )
                self.dynamic_critic = nn.Sequential(
                    nn.Linear(actual_dim, 128),
                    nn.LayerNorm(128),
                    nn.ReLU(),
                    nn.Linear(128, 1)
                )

                actor_output = self.dynamic_actor(policy_input)
                critic_output = self.dynamic_critic(policy_input)

                if len(actor_output.shape) > 2:
                    actor_output = actor_output.squeeze(1)

                return Categorical(logits=actor_output), critic_output

            actor_output = self.actor(policy_input)
            critic_output = self.critic(policy_input)

            if len(actor_output.shape) > 2:
                actor_output = actor_output.squeeze(1)

            return Categorical(logits=actor_output), critic_output

        except Exception as e:
            dummy_logits = torch.zeros(1, 2)
            dummy_value = torch.zeros(1, 1)
            return Categorical(logits=dummy_logits), dummy_value