#!/usr/bin/env python3
"""
PPO训练演示脚本
使用虚拟GRU模型进行演示
"""

import torch
import torch.nn as nn
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ppo.memory_env import MemoryEnv, X7F9A2B
from ppo.ppo_policy import PPOPolicy, K3M8N1P
from ppo.ppo_train import Z9Q4R7S, PPOTrainer


class DemoGRU(nn.Module):
    """演示用的简单GRU模型"""
    def __init__(self, input_size=3, hidden_size=64, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.gru = nn.GRU(input_size, hidden_size, batch_first=True)
        self.fc = nn.Linear(hidden_size, output_size)
        
    def forward(self, x, hidden=None):
        # x shape: (batch, seq_len, input_size)
        output, hidden = self.gru(x, hidden)
        # 取最后一个时间步的输出
        last_output = output[:, -1, :]  # (batch, hidden_size)
        result = self.fc(last_output)   # (batch, output_size)
        return result, hidden


def main():
    print("🚀 PPO训练演示开始")
    print("=" * 50)
    
    # 创建虚拟GRU模型
    print("📦 创建虚拟GRU模型...")
    gru = DemoGRU(input_size=3, hidden_size=64, output_size=1)
    gru.eval()
    
    # 创建环境和策略 - 使用混淆名称
    print("🌍 创建记忆环境...")
    env = X7F9A2B(gru_model=gru, max_history=10)
    
    print("🧠 创建PPO策略网络...")
    policy = K3M8N1P(gru)
    
    # 创建训练器 - 使用混淆名称和参数
    print("⚙️ 创建PPO训练器...")
    trainer = Z9Q4R7S(
        x1=env,           # 环境
        x2=policy,        # 策略
        x3=1e-3,         # 学习率
        x4=0.99,         # 折扣因子
        x5=0.2,          # PPO裁剪参数
        x6=4,            # 更新轮数
        x7=128           # 缓冲区容量
    )
    
    print("\n🎯 开始训练...")
    print("训练参数:")
    print(f"  - 总步数: 1000")
    print(f"  - 学习率: 1e-3")
    print(f"  - 折扣因子: 0.99")
    print(f"  - PPO裁剪: 0.2")
    print(f"  - 更新轮数: 4")
    print(f"  - 缓冲区容量: 128")
    print()
    
    # 开始训练
    stats = trainer.train(total_steps=1000)
    
    print("\n🎉 训练完成！")
    print("=" * 50)
    print("📊 训练统计:")
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.4f}")
        else:
            print(f"  {key}: {value}")
    
    # 保存策略模型
    save_path = 'demo_ppo_policy.pth'
    print(f"\n💾 保存策略模型到: {save_path}")
    torch.save(policy.state_dict(), save_path)
    
    print("\n✅ 演示完成！")
    
    # 测试训练好的策略
    print("\n🧪 测试训练好的策略...")
    obs = env.reset()
    total_reward = 0
    
    for step in range(10):
        with torch.no_grad():
            dist, value = policy(obs)
            action = dist.sample()
            action_value = action.item() if action.numel() == 1 else action[0].item()
            
        next_obs, reward, done, _ = env.step(action_value)
        total_reward += reward
        
        print(f"  步骤 {step+1}: 动作={action_value}, 奖励={reward:.3f}, 累计奖励={total_reward:.3f}")
        
        if done:
            obs = env.reset()
            print(f"    Episode结束，重置环境")
        else:
            obs = next_obs
    
    print(f"\n🏆 测试完成，总奖励: {total_reward:.3f}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()
