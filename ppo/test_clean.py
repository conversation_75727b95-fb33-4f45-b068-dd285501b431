#!/usr/bin/env python3

import torch
import torch.nn as nn
import numpy as np
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ppo.memory_env import MemoryEnv
from ppo.ppo_policy import PPOPolicy
from ppo.ppo_train import PPOTrainer, ExperienceBuffer


class SimpleGRU(nn.Module):
    def __init__(self, input_size=3, hidden_size=64, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.gru = nn.GRU(input_size, hidden_size, batch_first=True)
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x, hidden=None):
        output, hidden = self.gru(x, hidden)
        last_output = output[:, -1, :]
        result = self.fc(last_output)
        return result, hidden


def test_clean_code():
    print("Testing clean PPO implementation...")

    gru = SimpleGRU(input_size=3, hidden_size=64, output_size=1)
    gru.eval()

    env = MemoryEnv(gru_model=gru, max_history=5)
    policy = PPOPolicy(gru)
    trainer = PPOTrainer(env, policy, lr=1e-3, gamma=0.99, eps_clip=0.2, k_epochs=2, buffer_capacity=32)

    print("Starting training...")
    stats = trainer.train(total_steps=100)

    print("Training completed!")
    print(f"Episodes: {stats['total_episodes']}")
    print(f"Average reward: {stats['avg_reward']:.3f}")
    print(f"Average loss: {stats['avg_loss']:.3f}")
    print(f"Training time: {stats['training_time']:.2f}s")

    print("Testing policy...")
    obs = env.reset()
    for i in range(5):
        with torch.no_grad():
            dist, value = policy(obs)
            action = dist.sample()
            action_value = action.item() if action.numel() == 1 else action[0].item()

        next_obs, reward, done, _ = env.step(action_value)
        print(f"Step {i+1}: action={action_value}, reward={reward:.3f}")

        if done:
            obs = env.reset()
        else:
            obs = next_obs

    print("All tests passed!")


if __name__ == "__main__":
    test_clean_code()
