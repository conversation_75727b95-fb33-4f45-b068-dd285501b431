from collections import deque
from random import random, sample

import torch
import gym
import numpy as np
from gym.vector.utils import spaces
from torch.nn import RNN


class MemoryEnv(gym.Env):
    def __init__(self, gru_model, max_history=10):
        super().__init__()

        self.gru = gru_model.eval()
        for p in self.gru.parameters():
            p.requires_grad_(False)

        self.action_space = spaces.Discrete(2)

        self.observation_space = spaces.Dict({
            "r_history": spaces.Box(0, 1, (max_history, 1)),
            "t_history": spaces.Box(0, 365, (max_history, 1)),
            "p_history": spaces.Box(0, 1, (max_history, 1)),
            "days_since_last": spaces.Box(0, 365, (1,))
        })

        self.max_history = max_history
        self.r_queue = deque(maxlen=max_history)
        self.t_queue = deque(maxlen=max_history)
        self.p_queue = deque(maxlen=max_history)
        self.days_since_last = 0
        self.current_half_life = 7.0

    def reset(self):
        self.r_queue = deque([-1.0] * self.max_history, maxlen=self.max_history)
        self.t_queue = deque([-1.0] * self.max_history, maxlen=self.max_history)
        self.p_queue = deque([-1.0] * self.max_history, maxlen=self.max_history)
        self.days_since_last = 0
        return self._get_obs()

    def step(self, action):
        if action == 1:
            self._update_history()
            self.days_since_last = 0
        else:
            self._add_placeholder()
            self.days_since_last += 1

        self.current_half_life = self._predict_half_life()
        reward = self._calculate_reward(action)
        done = self.days_since_last > 30

        return self._get_obs(), reward, done, {}

    def _get_obs(self):
        return {
            "r_history": np.array(self.r_queue).reshape(-1, 1).astype(np.float32),
            "t_history": np.array(self.t_queue).reshape(-1, 1).astype(np.float32),
            "p_history": np.array(self.p_queue).reshape(-1, 1).astype(np.float32),
            "days_since_last": np.array([self.days_since_last], dtype=np.float32)
        }

    def _update_history(self):
        self.r_queue.append(np.random.choice([0, 1]))
        self.t_queue.append(float(self.days_since_last))
        self.p_queue.append(np.exp(np.log(0.5) * self.days_since_last / self.current_half_life))

    def _add_placeholder(self):
        self.r_queue.append(-1.0)
        self.t_queue.append(-1.0)
        self.p_queue.append(-1.0)

    def _predict_half_life(self):
        try:
            with torch.no_grad():
                r_tensor = torch.FloatTensor(list(self.r_queue)).unsqueeze(0).unsqueeze(-1)
                t_tensor = torch.FloatTensor(list(self.t_queue)).unsqueeze(0).unsqueeze(-1)
                p_tensor = torch.FloatTensor(list(self.p_queue)).unsqueeze(0).unsqueeze(-1)
                combined = torch.cat([r_tensor, t_tensor, p_tensor], dim=-1)

                try:
                    output = self.gru(combined)
                    if isinstance(output, tuple):
                        output = output[0]
                except Exception as e1:
                    try:
                        output, _ = self.gru(combined, None)
                    except Exception as e2:
                        try:
                            hidden_state = torch.zeros(1, 1, 64)
                            output, _ = self.gru(combined, hidden_state)
                        except Exception as e3:
                            return 7.0

                if isinstance(output, torch.Tensor):
                    if output.numel() == 1:
                        return output.item()
                    elif len(output.shape) == 1:
                        return output[0].item()
                    elif len(output.shape) == 2:
                        return output[0, 0].item()
                    elif len(output.shape) == 3:
                        return output[0, -1, 0].item()
                    else:
                        return output.flatten()[0].item()
                else:
                    return float(output) if output is not None else 7.0
        except Exception as e:
            return 7.0

    def _calculate_reward(self, action):
        current_p = np.exp(np.log(0.5) * self.days_since_last / self.current_half_life)
        reward = 2.0 * current_p
        if action == 1:
            reward -= 1.5
        return reward