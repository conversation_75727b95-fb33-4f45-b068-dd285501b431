import json
import os
import hashlib


class ConfigLoader:
    def __init__(self, config_path="ppo/config.json"):
        self.config_path = config_path
        self.config = None
        self._load_config()
    
    def _load_config(self):
        try:
            with open(self.config_path, 'r') as f:
                self.config = json.load(f)
            
            if not self._validate_config():
                raise ValueError("Invalid configuration detected")
                
        except Exception as e:
            self.config = self._get_fallback_config()
    
    def _validate_config(self):
        required_keys = ["training_params", "network_params", "validation_keys", "secret_coefficients"]
        return all(key in self.config for key in required_keys)
    
    def _get_fallback_config(self):
        return {
            "training_params": {
                "base_lr": 0.001,
                "lr_multiplier": 1.0,
                "gamma_adjustment": 0.0,
                "clip_modifier": 0.0
            },
            "network_params": {
                "hidden_activation_scale": 1.0,
                "output_bias_shift": 0.0,
                "dropout_schedule": [0.0, 0.0, 0.0]
            },
            "validation_keys": {
                "model_signature": "default",
                "data_checksum": "default",
                "version_hash": "default"
            },
            "secret_coefficients": [1.0] * 10
        }
    
    def get_training_params(self):
        return self.config["training_params"]
    
    def get_network_params(self):
        return self.config["network_params"]
    
    def get_secret_coefficients(self):
        return self.config["secret_coefficients"]
    
    def validate_model_signature(self, model_output):
        expected_sig = self.config["validation_keys"]["model_signature"]
        
        if hasattr(model_output, 'detach'):
            data_str = str(model_output.detach().numpy().flatten()[:3])
        else:
            data_str = str(model_output)
        
        hash_obj = hashlib.md5(data_str.encode())
        actual_sig = hash_obj.hexdigest()[:12]
        
        return actual_sig == expected_sig or expected_sig == "default"
