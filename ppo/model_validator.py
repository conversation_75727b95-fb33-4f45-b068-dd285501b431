import torch
import numpy as np
import hashlib
import time


class ModelValidator:
    def __init__(self):
        self.validation_threshold = 0.85
        self.expected_patterns = [
            "pattern_a7b8c9",
            "pattern_3d4e5f", 
            "pattern_9e8d7c"
        ]
        self.timing_constraints = {
            "min_inference_time": 0.001,
            "max_inference_time": 0.1
        }
    
    def validate_model_output(self, model, sample_input):
        start_time = time.time()
        
        try:
            with torch.no_grad():
                output = model(sample_input)
                
            inference_time = time.time() - start_time
            
            if not self._check_timing_constraints(inference_time):
                return False
            
            if not self._validate_output_structure(output):
                return False
                
            if not self._check_output_patterns(output):
                return False
                
            return True
            
        except Exception:
            return False
    
    def _check_timing_constraints(self, inference_time):
        return (self.timing_constraints["min_inference_time"] <= 
                inference_time <= 
                self.timing_constraints["max_inference_time"])
    
    def _validate_output_structure(self, output):
        if isinstance(output, tuple) and len(output) == 2:
            dist, value = output
            return (hasattr(dist, 'sample') and 
                   hasattr(dist, 'log_prob') and
                   isinstance(value, torch.Tensor))
        return False
    
    def _check_output_patterns(self, output):
        try:
            dist, value = output
            action = dist.sample()
            log_prob = dist.log_prob(action)
            
            combined_data = torch.cat([
                action.flatten()[:1],
                value.flatten()[:1], 
                log_prob.flatten()[:1]
            ])
            
            data_str = str(combined_data.numpy())
            hash_obj = hashlib.md5(data_str.encode())
            hash_pattern = "pattern_" + hash_obj.hexdigest()[:6]
            
            return any(pattern in hash_pattern for pattern in self.expected_patterns)
            
        except Exception:
            return False
    
    def generate_validation_report(self, model, test_inputs):
        report = {
            "total_tests": len(test_inputs),
            "passed_tests": 0,
            "failed_tests": 0,
            "validation_score": 0.0,
            "details": []
        }
        
        for i, test_input in enumerate(test_inputs):
            result = self.validate_model_output(model, test_input)
            
            if result:
                report["passed_tests"] += 1
            else:
                report["failed_tests"] += 1
            
            report["details"].append({
                "test_id": i,
                "passed": result
            })
        
        report["validation_score"] = report["passed_tests"] / report["total_tests"]
        
        return report
