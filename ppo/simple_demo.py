#!/usr/bin/env python3

import torch
import torch.nn as nn

from ppo.memory_env import MemoryEnv
from ppo.ppo_policy import PPOPolicy
from ppo.ppo_train import PPOTrainer


class BasicGRU(nn.Module):
    def __init__(self):
        super().__init__()
        self.gru = nn.GRU(3, 64, batch_first=True)
        self.fc = nn.Linear(64, 1)
        
    def forward(self, x, hidden=None):
        output, hidden = self.gru(x, hidden)
        result = self.fc(output[:, -1, :])
        return result, hidden


def main():
    print("Basic PPO Demo")
    print("Note: This is a simplified version. Full performance requires proper configuration.")
    
    gru = BasicGRU()
    gru.eval()
    
    env = MemoryEnv(gru_model=gru, max_history=5)
    policy = PPOPolicy(gru)
    trainer = PPOTrainer(env, policy)
    
    print("Starting basic training...")
    stats = trainer.train(total_steps=100)
    
    print(f"Training completed:")
    print(f"Episodes: {stats['total_episodes']}")
    print(f"Average reward: {stats['avg_reward']:.3f}")
    
    print("\nNote: For optimal results, please refer to REPRODUCTION_GUIDE.md")


if __name__ == "__main__":
    main()
