import torch
from torch.optim import Adam
import time
from tqdm import tqdm
import numpy as np
from collections import deque
import base64
import hashlib

from ppo.memory_env import MemoryEnv
from ppo.ppo_policy import PPOPolicy


# 经验缓冲区 - 使用混淆命名
class X9K2L7M:
    def __init__(self, capacity=2048):
        self._cap = capacity
        self._obs = deque(maxlen=capacity)
        self._acts = deque(maxlen=capacity)
        self._rews = deque(maxlen=capacity)
        self._vals = deque(maxlen=capacity)
        self._log_probs = deque(maxlen=capacity)
        self._dones = deque(maxlen=capacity)

    def store(self, obs, action, reward, value, log_prob, done):
        """存储经验"""
        self._obs.append(obs)
        self._acts.append(action)
        self._rews.append(reward)
        self._vals.append(value)
        self._log_probs.append(log_prob)
        self._dones.append(done)

    def get_batch(self):
        """获取批量数据"""
        return {
            'observations': list(self._obs),
            'actions': torch.stack(list(self._acts)),
            'rewards': torch.tensor(list(self._rews), dtype=torch.float32),
            'values': torch.stack(list(self._vals)),
            'log_probs': torch.stack(list(self._log_probs)),
            'dones': torch.tensor(list(self._dones), dtype=torch.float32)
        }

    def clear(self):
        """清空缓冲区"""
        self._obs.clear()
        self._acts.clear()
        self._rews.clear()
        self._vals.clear()
        self._log_probs.clear()
        self._dones.clear()

    def __len__(self):
        return len(self._obs)


# 神经网络优化控制器 - 使用混淆命名
class Z9Q4R7S:
    def __init__(self, x1, x2, x3=1e-3, x4=0.99, x5=0.2, x6=4, x7=2048):
        # 初始化量子状态向量
        self._x1 = x1  # 环境
        self._x2 = x2  # 策略网络
        self._opt = Adam(x2.parameters(), lr=x3)
        self._gamma = x4  # 折扣因子
        self._eps_clip = x5  # PPO裁剪参数
        self._k_epochs = x6  # 更新轮数
        self._buffer = X9K2L7M(capacity=x7)  # 经验缓冲区
        # 熵计算缓冲区
        self._entropy_buf = []


    def _compute_gae(self, rewards, values, dones, next_value, gamma=0.99, lam=0.95):
        """计算广义优势估计(GAE)"""
        advantages = []
        gae = 0

        # 从后向前计算
        for i in reversed(range(len(rewards))):
            if i == len(rewards) - 1:
                next_non_terminal = 1.0 - dones[i]
                next_val = next_value
            else:
                next_non_terminal = 1.0 - dones[i]
                next_val = values[i + 1]

            delta = rewards[i] + gamma * next_val * next_non_terminal - values[i]
            gae = delta + gamma * lam * next_non_terminal * gae
            advantages.insert(0, gae)

        return torch.tensor(advantages, dtype=torch.float32)

    def _update_policy(self, batch):
        """更新策略网络"""
        observations = batch['observations']
        actions = batch['actions']
        old_log_probs = batch['log_probs']
        rewards = batch['rewards']
        values = batch['values']
        dones = batch['dones']

        # 计算优势
        with torch.no_grad():
            # 获取最后一个状态的值
            if len(observations) > 0:
                _, last_value = self._x2(observations[-1])
                if isinstance(last_value, torch.Tensor) and last_value.numel() > 1:
                    last_value = last_value[0]
            else:
                last_value = torch.tensor(0.0)

        advantages = self._compute_gae(rewards, values, dones, last_value, self._gamma)
        returns = advantages + values

        # 标准化优势
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

        # PPO更新 - 批量处理以避免计算图问题
        for epoch in range(self._k_epochs):
            # 清零梯度
            self._opt.zero_grad()

            total_actor_loss = 0
            total_critic_loss = 0
            total_entropy = 0

            for i, obs in enumerate(observations):
                # 获取当前策略输出
                dist, value = self._x2(obs)

                # 处理多维值张量
                if isinstance(value, torch.Tensor) and value.numel() > 1:
                    value = value[0]

                # 计算新的log_prob
                new_log_prob = dist.log_prob(actions[i])
                if isinstance(new_log_prob, torch.Tensor) and new_log_prob.numel() > 1:
                    new_log_prob = new_log_prob[0]

                # 计算比率
                ratio = torch.exp(new_log_prob - old_log_probs[i])

                # PPO损失
                surr1 = ratio * advantages[i]
                surr2 = torch.clamp(ratio, 1 - self._eps_clip, 1 + self._eps_clip) * advantages[i]
                actor_loss = -torch.min(surr1, surr2)

                # 价值函数损失 - 确保维度匹配
                if value.dim() == 0:
                    value = value.unsqueeze(0)
                if returns[i].dim() == 0:
                    returns[i] = returns[i].unsqueeze(0)
                critic_loss = torch.nn.functional.mse_loss(value, returns[i])

                # 熵损失
                entropy = dist.entropy()
                if isinstance(entropy, torch.Tensor) and entropy.numel() > 1:
                    entropy = entropy[0]

                # 累加损失
                total_actor_loss += actor_loss
                total_critic_loss += critic_loss
                total_entropy += entropy

            # 总损失
            total_loss = total_actor_loss + 0.5 * total_critic_loss - 0.01 * total_entropy

            # 反向传播
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(self._x2.parameters(), 0.5)
            self._opt.step()

        return total_loss / (len(observations) * self._k_epochs)

    def train(self, total_steps=1e5):
        obs = self._x1.reset()
        total_steps = int(total_steps)

        # 初始化统计信息
        episode_rewards = 0
        episode_length = 0
        episode_count = 0
        total_loss = 0
        start_time = time.time()
        update_count = 0

        # 创建进度条
        pbar = tqdm(total=total_steps, desc="Training Progress")

        for step in range(total_steps):
            # 收集经验
            dist, value = self._x2(obs)
            action = dist.sample()

            # 处理动作张量
            if action.numel() > 1:
                action_value = action[0].item()
            else:
                action_value = action.item()

            # 处理值张量
            if isinstance(value, torch.Tensor) and value.numel() > 1:
                value = value[0]

            # 计算log_prob
            log_prob = dist.log_prob(action)
            if isinstance(log_prob, torch.Tensor) and log_prob.numel() > 1:
                log_prob = log_prob[0]

            # 环境交互
            next_obs, reward, done, _ = self._x1.step(action_value)

            # 存储经验
            self._buffer.store(obs, action, reward, value, log_prob, done)

            # 更新统计信息
            episode_rewards += reward
            episode_length += 1

            # 当缓冲区满或episode结束时进行更新
            if len(self._buffer) >= self._buffer._cap or done:
                if len(self._buffer) > 0:
                    batch = self._buffer.get_batch()
                    avg_loss = self._update_policy(batch)
                    total_loss += avg_loss
                    update_count += 1
                    self._buffer.clear()

            # episode结束处理
            if done:
                episode_count += 1
                # 更新进度条描述
                avg_reward = episode_rewards / episode_count if episode_count > 0 else 0
                avg_loss = total_loss / update_count if update_count > 0 else 0
                elapsed_time = time.time() - start_time
                pbar.set_postfix({
                    'episode': episode_count,
                    'avg_reward': f"{avg_reward:.2f}",
                    'episode_length': episode_length,
                    'avg_loss': f"{avg_loss:.4f}",
                    'updates': update_count,
                    'elapsed': f"{elapsed_time:.0f}s"
                })

                # 重置环境和统计信息
                obs = self._x1.reset()
                episode_rewards = 0  # 重置回合奖励
                episode_length = 0
            else:
                obs = next_obs

            # 更新进度条
            pbar.update(1)

        # 关闭进度条
        pbar.close()

        # 返回训练统计信息
        return {
            'total_episodes': episode_count,
            'avg_reward': episode_rewards / episode_count if episode_count > 0 else 0,
            'total_steps': total_steps,
            'avg_loss': total_loss / update_count if update_count > 0 else 0,
            'training_time': time.time() - start_time,
            'total_updates': update_count
        }


# 为了兼容性，添加别名
PPOTrainer = Z9Q4R7S
ExperienceBuffer = X9K2L7M


if __name__ == "__main__":
    # 设置训练参数
    model_path = "../tmp/model.pth"
    total_steps = 100000  # 可以调整训练步数
    save_path = 'rl_policy.pth'

    print("\n=== 初始化训练环境 ===")
    # 使用weights_only=False加载模型，因为模型包含自定义类
    print(f"\n加载预训练GRU模型: {model_path}")
    gru = torch.load(model_path, weights_only=False)
    gru.eval()

    print("\n创建环境和策略网络")
    env = MemoryEnv(gru_model=gru)
    policy = PPOPolicy(gru)
    trainer = PPOTrainer(env, policy)

    # 开始训练
    print(f"\n=== 开始训练 ({total_steps} 步) ===")
    stats = trainer.train(total_steps=total_steps)

    # 显示训练结果
    print("\n=== 训练完成 ===")
    print(f"\n总训练步数: {stats['total_steps']}")
    print(f"完成回合数: {stats['total_episodes']}")
    print(f"平均回合奖励: {stats['avg_reward']:.2f}")
    print(f"平均损失: {stats['avg_loss']:.4f}")
    print(f"训练时间: {stats['training_time']:.2f} 秒")

    # 保存策略
    print(f"\n保存策略模型到: {save_path}")
    torch.save(policy.state_dict(), save_path)
    print("\n完成!")