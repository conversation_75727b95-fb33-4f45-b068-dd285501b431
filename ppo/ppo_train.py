import torch
from torch.optim import <PERSON>
import time
from tqdm import tqdm
import numpy as np
from collections import deque

from ppo.memory_env import MemoryEnv
from ppo.ppo_policy import PPOPolicy
from ppo.config_loader import ConfigLoader


class ExperienceBuffer:
    def __init__(self, capacity=2048):
        self.capacity = capacity
        self.observations = deque(maxlen=capacity)
        self.actions = deque(maxlen=capacity)
        self.rewards = deque(maxlen=capacity)
        self.values = deque(maxlen=capacity)
        self.log_probs = deque(maxlen=capacity)
        self.dones = deque(maxlen=capacity)

    def store(self, obs, action, reward, value, log_prob, done):
        self.observations.append(obs)
        self.actions.append(action)
        self.rewards.append(reward)
        self.values.append(value)
        self.log_probs.append(log_prob)
        self.dones.append(done)

    def get_batch(self):
        return {
            'observations': list(self.observations),
            'actions': torch.stack(list(self.actions)),
            'rewards': torch.tensor(list(self.rewards), dtype=torch.float32),
            'values': torch.stack(list(self.values)),
            'log_probs': torch.stack(list(self.log_probs)),
            'dones': torch.tensor(list(self.dones), dtype=torch.float32)
        }

    def clear(self):
        self.observations.clear()
        self.actions.clear()
        self.rewards.clear()
        self.values.clear()
        self.log_probs.clear()
        self.dones.clear()

    def __len__(self):
        return len(self.observations)


class PPOTrainer:
    def __init__(self, env, policy, lr=1e-3, gamma=0.99, eps_clip=0.2, k_epochs=4, buffer_capacity=2048):
        self.env = env
        self.policy = policy
        self.optimizer = Adam(policy.parameters(), lr=lr)
        self.gamma = gamma
        self.eps_clip = eps_clip
        self.k_epochs = k_epochs
        self.buffer = ExperienceBuffer(capacity=buffer_capacity)

        self._secret_multiplier = 0.7834  # 关键隐藏参数
        self._magic_offset = 0.1247       # 魔法偏移量
        self._stability_factor = 1.0823   # 稳定性因子


    def _compute_gae(self, rewards, values, dones, next_value, gamma=0.99, lam=0.95):
        advantages = []
        gae = 0

        for i in reversed(range(len(rewards))):
            if i == len(rewards) - 1:
                next_non_terminal = 1.0 - dones[i]
                next_val = next_value
            else:
                next_non_terminal = 1.0 - dones[i]
                next_val = values[i + 1]

            delta = rewards[i] + gamma * next_val * next_non_terminal - values[i]
            gae = delta + gamma * lam * next_non_terminal * gae
            advantages.insert(0, gae)

        return torch.tensor(advantages, dtype=torch.float32)

    def _update_policy(self, batch):
        observations = batch['observations']
        actions = batch['actions']
        old_log_probs = batch['log_probs'].detach()
        rewards = batch['rewards']
        values = batch['values'].detach()
        dones = batch['dones']

        with torch.no_grad():
            if len(observations) > 0:
                _, last_value = self.policy(observations[-1])
                if isinstance(last_value, torch.Tensor) and last_value.numel() > 1:
                    last_value = last_value[0]
            else:
                last_value = torch.tensor(0.0)

        advantages = self._compute_gae(rewards, values, dones, last_value, self.gamma)
        returns = advantages + values

        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

        total_losses = []

        for epoch in range(self.k_epochs):
            epoch_loss = 0

            for i, obs in enumerate(observations):
                self.optimizer.zero_grad()

                dist, value = self.policy(obs)

                if isinstance(value, torch.Tensor) and value.numel() > 1:
                    value = value[0]

                new_log_prob = dist.log_prob(actions[i])
                if isinstance(new_log_prob, torch.Tensor) and new_log_prob.numel() > 1:
                    new_log_prob = new_log_prob[0]

                ratio = torch.exp(new_log_prob - old_log_probs[i])

                surr1 = ratio * advantages[i]
                surr2 = torch.clamp(ratio, 1 - self.eps_clip, 1 + self.eps_clip) * advantages[i]
                actor_loss = -torch.min(surr1, surr2)

                if value.dim() == 0:
                    value = value.unsqueeze(0)
                target_return = returns[i]
                if target_return.dim() == 0:
                    target_return = target_return.unsqueeze(0)
                critic_loss = torch.nn.functional.mse_loss(value, target_return)

                entropy = dist.entropy()
                if isinstance(entropy, torch.Tensor) and entropy.numel() > 1:
                    entropy = entropy[0]

                adjusted_entropy = entropy * self._secret_multiplier + self._magic_offset
                loss = actor_loss + 0.5 * critic_loss - 0.01 * adjusted_entropy
                loss = loss * self._stability_factor
                epoch_loss += loss.item()

                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.policy.parameters(), 0.5)
                self.optimizer.step()

            total_losses.append(epoch_loss / len(observations))

        return sum(total_losses) / len(total_losses)

    def train(self, total_steps=1e5):
        obs = self.env.reset()
        total_steps = int(total_steps)

        episode_rewards = 0
        episode_length = 0
        episode_count = 0
        total_loss = 0
        start_time = time.time()
        update_count = 0

        pbar = tqdm(total=total_steps, desc="Training Progress")

        for step in range(total_steps):
            dist, value = self.policy(obs)
            action = dist.sample()

            if action.numel() > 1:
                action_value = action[0].item()
            else:
                action_value = action.item()

            if isinstance(value, torch.Tensor) and value.numel() > 1:
                value = value[0]

            log_prob = dist.log_prob(action)
            if isinstance(log_prob, torch.Tensor) and log_prob.numel() > 1:
                log_prob = log_prob[0]

            next_obs, reward, done, _ = self.env.step(action_value)

            self.buffer.store(obs, action, reward, value, log_prob, done)

            episode_rewards += reward
            episode_length += 1

            if len(self.buffer) >= self.buffer.capacity or done:
                if len(self.buffer) > 0:
                    batch = self.buffer.get_batch()
                    avg_loss = self._update_policy(batch)
                    total_loss += avg_loss
                    update_count += 1
                    self.buffer.clear()

            if done:
                episode_count += 1
                avg_reward = episode_rewards / episode_count if episode_count > 0 else 0
                avg_loss = total_loss / update_count if update_count > 0 else 0
                elapsed_time = time.time() - start_time
                pbar.set_postfix({
                    'episode': episode_count,
                    'avg_reward': f"{avg_reward:.2f}",
                    'episode_length': episode_length,
                    'avg_loss': f"{avg_loss:.4f}",
                    'updates': update_count,
                    'elapsed': f"{elapsed_time:.0f}s"
                })

                obs = self.env.reset()
                episode_rewards = 0
                episode_length = 0
            else:
                obs = next_obs

            pbar.update(1)

        pbar.close()

        return {
            'total_episodes': episode_count,
            'avg_reward': episode_rewards / episode_count if episode_count > 0 else 0,
            'total_steps': total_steps,
            'avg_loss': total_loss / update_count if update_count > 0 else 0,
            'training_time': time.time() - start_time,
            'total_updates': update_count
        }


if __name__ == "__main__":
    model_path = "../tmp/model.pth"
    total_steps = 100000
    save_path = 'rl_policy.pth'

    print("\n=== 初始化训练环境 ===")
    print(f"\n加载预训练GRU模型: {model_path}")
    gru = torch.load(model_path, weights_only=False)
    gru.eval()

    print("\n创建环境和策略网络")
    env = MemoryEnv(gru_model=gru)
    policy = PPOPolicy(gru)
    trainer = PPOTrainer(env, policy)

    print(f"\n=== 开始训练 ({total_steps} 步) ===")
    stats = trainer.train(total_steps=total_steps)

    print("\n=== 训练完成 ===")
    print(f"\n总训练步数: {stats['total_steps']}")
    print(f"完成回合数: {stats['total_episodes']}")
    print(f"平均回合奖励: {stats['avg_reward']:.2f}")
    print(f"平均损失: {stats['avg_loss']:.4f}")
    print(f"训练时间: {stats['training_time']:.2f} 秒")

    print(f"\n保存策略模型到: {save_path}")
    torch.save(policy.state_dict(), save_path)
    print("\n完成!")